<script setup lang="ts">
import { baseApi } from '~/service/modules/base'
import { useDictStore } from '~/stores'

defineOptions({
  name: 'TalentResume',
})

const useDict = useDictStore()

const imageBaseUrl = import.meta.env.VITE_STATIC_BASE_URL

const list = ref<any>()

const type = ref(0)

const edu = ref(0)

const filter = ref({
  _page: 1,
  _limit: 20,
  _expand: 'company',
  q: '',
  type,
  edu,
})

async function getData() {
  const res = await baseApi.talentList(filter.value)
  if (res.code === 200) {
    list.value = res.data
  }
}

onMounted(() => {
  getData()
})

watch(type, () => getData())
watch(edu, () => getData())
</script>

<template>
  <div class="resume">
    <div class="search p-24px bg-white">
      <div class="keyword p-x-32px b-rd-4px flex h-128px w-full items-center">
        <div class="text-white w-40%">
          <div class="text-28px lh-40px">
            企业人才库
          </div>
          <div class="text-16px lh-22px m-t-8px opacity-80">
            汇聚行业精英，构建未来人才梯队
          </div>
        </div>
        <NInputGroup class="w-60%">
          <NInput v-model:value="filter.q" size="large" placeholder="请输入关键词" clearable>
            <template #prefix>
              <span class="i-carbon-search text-16px" />
            </template>
          </NInput>
          <NButton size="large" type="info" class="w-104px" @click="getData">
            查询
          </NButton>
        </NInputGroup>
      </div>
      <div class="text-18px lh-25px m-t-24px filter">
        <div class="flex items-center justify-between">
          <div class="label text-#4B5B76 text-right w-120px">
            类型：
          </div>
          <div class="w-1000px">
            <NRadioGroup v-model:value="type">
              <NRadioButton :key="0" :value="0">
                全部
              </NRadioButton>
              <NRadioButton v-for="item in useDict.get('JobType')" :key="item.value" :value="item.value">
                {{ item.label }}
              </NRadioButton>
            </NRadioGroup>
          </div>
        </div>
        <div class="line flex items-center justify-between">
          <div class="label text-#4B5B76 text-right w-120px">
            学历：
          </div>
          <div class="w-1000px">
            <NRadioGroup v-model:value="edu">
              <NRadioButton :key="0" :value="0">
                全部
              </NRadioButton>
              <NRadioButton v-for="item in useDict.get('Edu')" :key="item.value" :value="item.value">
                {{ item.label }}
              </NRadioButton>
            </NRadioGroup>
          </div>
        </div>
      </div>
    </div>
    <div class="list m-t-24px">
      <div v-if="list?.records.length > 0" class="flex flex-wrap gap-24px">
        <RouterLink v-for="item in list?.records" :key="item.id" :to="`/talent/resume_${item.id}`" class="item p-24px b-rd-4px flex items-start justify-between overflow-hidden">
          <div class="avatar b-rd-4px overflow-hidden">
            <img class="h-120px w-120px" :src="imageBaseUrl + item.avatar" alt="avatar">
          </div>
          <div class="info w-400px">
            <div class="flex w-full items-center justify-between">
              <div class="text-20px lh-28px font-medium">
                {{ item.name }}
              </div>
              <div class="flex gap-x-8px items-center justify-end">
                <NButton type="primary" ghost size="small" class="text-14px h-34px w-88px">
                  <span>查看详情</span>
                </NButton>
              </div>
            </div>
            <div class="text-14px text-#98A3B7 lh-20px m-t-16px flex gap-x-8px">
              <span>{{ item.gender }}</span>
              <span>|</span>
              <span>{{ item.age }}岁</span>
              <span>|</span>
              <span>{{ item.exp }}</span>
              <span>|</span>
              <span>{{ item.edu }}</span>
            </div>
            <div class="text-16px lh-20px m-t-16px">
              <span>专业技能：</span>
              <span>{{ item.skills.join('，') }}</span>
            </div>
          </div>
        </RouterLink>
      </div>
      <NEmpty v-else size="large" description="暂无数据" class="p-y-120px" />
    </div>
    <div class="m-t-36px flex items-center justify-center">
      <NPagination
        v-model:page="filter._page"
        :page-count="100"
        size="large"
        show-quick-jumper
        show-size-picker
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.resume {
  padding-bottom: 40px;
  .search {
    .keyword {
      background: url('/assets/images/<EMAIL>') no-repeat center center / cover;
      :deep(.n-input) {
        --n-height: 56px !important;
        --n-color: #f5f7fa !important;
        --n-border: #f5f7fa !important;
        --n-font-size: 16px !important;
      }
      :deep(.n-button) {
        --n-height: 56px !important;
      }
    }
    .filter {
      .line {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f5f7fa;
      }
      :deep(.n-radio-group) {
        display: flex;
        gap: 0 24px;
        .n-radio-group__splitor,
        .n-radio-button__state-border {
          display: none;
        }
        .n-radio-button {
          border: 0;
          border-radius: 4px;
          font-size: 18px;
          color: #181818;
        }
        .n-radio-button--checked {
          background: #ccdeff;
          color: #0052d9;
        }
      }
    }
  }
  .list {
    .item {
      width: 588px;
      box-shadow: 0px 18px 36px 0px rgba(192, 199, 218, 0.3);
      transition: all 0.3s ease-in-out;
      background: url('/assets/images/<EMAIL>') no-repeat center center / 500% 500%;
      &:hover {
        transform: translateY(-10px);
        box-shadow: 0px 18px 36px 0px rgba(192, 199, 218, 0.5);
        background-size: 100% 100%;
      }
    }
  }
}
</style>
