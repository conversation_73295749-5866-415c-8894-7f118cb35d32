<script setup lang="ts">
import { showArea } from '~/utils/area'

defineOptions({
  name: 'HomeTalent',
})

defineProps<{
  data: any[] | undefined
}>()
</script>

<template>
  <div class="talent">
    <div class="p-y-64px container">
      <div class="text-28px lh-48px text-center">
        企业人才管理
      </div>
      <div class="text-16px text-#4B5B76 lh-48px m-t-8px flex gap-x-8px items-center justify-center">
        立足当下，以人为本
      </div>
      <div class="m-t-32px">
        <div class="hd flex items-center justify-between">
          <div class="title text-20px lh-30px flex gap-x-8px items-center">
            <img class="h-14px w-14px" src="/assets/images/<EMAIL>" alt="icon">
            <span>人才需求发布</span>
          </div>
          <RouterLink to="/talent?tab=0" class="text-16px text-primary lh-22px flex items-center justify-center hover:opacity-50">
            <span>查看更多职位</span>
            <span class="i-carbon-chevron-right text-[0.9em] text-primary" />
          </RouterLink>
        </div>
        <div class="bd m-t-32px flex flex-wrap gap-y-24px items-center justify-between">
          <RouterLink v-for="item in data" :key="item.url" :to="item.url" class="item p-24px b-rd-4px bg-white w-384px overflow-hidden">
            <div class="text-20px lh-22px font-bold flex items-center justify-between">
              <span>{{ item.title }}</span>
              <span v-if="item.salaryType === 1" class="text-#E70B0B">{{ item.salary[0] / 1000 }}~{{ item.salary[1] / 1000 }}K</span>
              <span v-else class="text-#E70B0B">面议</span>
            </div>
            <div class="m-t-16px flex items-center justify-between">
              <div class="flex gap-x-8px">
                <NTag v-for="tag in item.tags" :key="item.url + tag" :color="{ color: '#F1F2F5', textColor: '#4B5B76' }" size="small" :bordered="false">
                  {{ tag }}
                </NTag>
              </div>
              <div class="text-16px lh-22px">
                {{ showArea(item.area).join('-') }}
              </div>
            </div>
          </RouterLink>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.talent {
  background-color: #f5f7fa;
  .item {
    position: relative;
    box-shadow: 0px 18px 36px 0px rgba(192, 199, 218, 0.3);
    background: url('/assets/images/<EMAIL>') no-repeat center center / 500% 500%;
    transition: all 0.2s ease-in-out;
    &:before {
      content: '';
      position: absolute;
      z-index: 1;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #fff;
      opacity: 1;
      transition: all 0.2s ease-in-out;
    }
    &:hover {
      transform: translateY(-10px);
      background-size: 100% 100%;
      &:before {
        opacity: 0;
      }
    }
    .flex {
      position: relative;
      z-index: 2;
    }
  }
}
</style>
