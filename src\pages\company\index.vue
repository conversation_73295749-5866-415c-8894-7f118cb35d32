<script setup lang="ts">
import List from './modules/list.vue'

defineOptions({
  name: 'CompanyPage',
})
</script>

<template>
  <main>
    <NBreadcrumb separator=">" class="text-16px p-t-16px container">
      <NBreadcrumbItem href="/" class="flex gap-x-4px items-end">
        <div class="flex gap-x-4px items-end">
          <i class="i-carbon-home" />
          <span>首页</span>
        </div>
      </NBreadcrumbItem>
      <NBreadcrumbItem>
        企业库
      </NBreadcrumbItem>
    </NBreadcrumb>
    <div class="p-y-16px container">
      <List />
    </div>
  </main>
</template>

<style scoped lang="less">

</style>
