import { request } from '~/utils/http'

export const baseApi = {
  // 配置数据
  config() {
    return request<Api.Base.Config[]>({
      url: '/base/config',
      method: 'get',
    })
  },

  // 字典数据
  dict() {
    return request<Api.Base.Dict[]>({
      url: '/base/dict',
      method: 'get',
    })
  },

  // 首页数据
  home() {
    return request<any>({
      url: '/base/home',
      method: 'get',
    })
  },

  // 文章详情
  postDetail(id: any) {
    return request<any>({
      url: `/posts/${id}`,
      method: 'get',
    })
  },

  // 单页详情
  pageDetail(slug: string) {
    return request<any>({
      url: `/pages/${slug}`,
      method: 'get',
    })
  },

  // 报告列表
  reportList(params: any) {
    return request<any>({
      url: '/reports',
      method: 'get',
      params,
    })
  },

  // 报告详情
  reportDetail(id: any) {
    return request<any>({
      url: `/reports/${id}`,
      method: 'get',
    })
  },

  // 知识列表
  docList(params: any) {
    return request<any>({
      url: '/knowledges',
      method: 'get',
      params,
    })
  },

  // 知识详情
  docDetail(id: any) {
    return request<any>({
      url: `/knowledges/${id}`,
      method: 'get',
    })
  },

  // 专家列表
  expertList(params: any) {
    return request<any>({
      url: '/experts',
      method: 'get',
      params,
    })
  },

  // 专家详情
  expertDetail(id: any) {
    return request<any>({
      url: `/experts/${id}`,
      method: 'get',
    })
  },

  // 岗位列表
  jobList(params: any) {
    return request<any>({
      url: '/jobs',
      method: 'get',
      params,
    })
  },

  // 岗位详情
  jobDetail(id: any) {
    return request<any>({
      url: `/jobs/${id}`,
      method: 'get',
    })
  },

  // 人才列表
  talentList(params: any) {
    return request<any>({
      url: '/talents',
      method: 'get',
      params,
    })
  },

  // 人才详情
  talentDetail(id: any) {
    return request<any>({
      url: `/talents/${id}`,
      method: 'get',
    })
  },

  // 公司列表
  companyList(params: any) {
    return request<any>({
      url: '/companies',
      method: 'get',
      params,
    })
  },

  // 公司详情
  companyDetail(id: any) {
    return request<any>({
      url: `/companies/${id}`,
      method: 'get',
    })
  },

  // 产品列表
  productList(params: any) {
    return request<any>({
      url: '/products',
      method: 'get',
      params,
    })
  },
}
