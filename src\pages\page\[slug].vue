<script setup lang="ts">
import { baseApi } from '~/service/modules/base'
import Detail from './modules/detail.vue'

defineOptions({
  name: 'PageDetailPage',
})

const route = useRoute()

const detail = ref<any>()

async function getData() {
  const { slug } = route.params as { slug: string }
  const res = await baseApi.pageDetail(slug)
  if (res.code === 200) {
    detail.value = res.data
  }
}

onMounted(() => {
  getData()
})
</script>

<template>
  <main>
    <NBreadcrumb separator=">" class="text-16px p-t-16px container">
      <NBreadcrumbItem href="/" class="flex gap-x-4px items-end">
        <div class="flex gap-x-4px items-end">
          <i class="i-carbon-home" />
          <span>首页</span>
        </div>
      </NBreadcrumbItem>
      <NBreadcrumbItem>
        单页详情
      </NBreadcrumbItem>
    </NBreadcrumb>
    <div class="p-y-16px container">
      <Detail :data="detail" />
    </div>
  </main>
</template>

<style scoped lang="less">

</style>
