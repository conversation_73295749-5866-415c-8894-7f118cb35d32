<script setup lang="ts">
defineOptions({
  name: 'HomeReport',
})

const list = [
  {
    title: '科森云数字科技有限公司',
    summary: '助力企业以变革谋未来助力企业以变革谋未来助力企业以变革谋未来助力企业以变革谋未来助力企业以变革谋未来助力企业以变革谋未来',
    cover: '/upload/img1.png',
    logo: '/upload/company-logo.png',
    url: '/',
  },
  {
    title: '科森云数字科技有限公司',
    summary: '助力企业以变革谋未来助力企业以变革谋未来助力企业以变革谋未来助力企业以变革谋未来助力企业以变革谋未来助力企业以变革谋未来',
    cover: '/upload/img2.png',
    logo: '/upload/company-logo.png',
    url: '/',
  },
  {
    title: '科森云数字科技有限公司',
    summary: '助力企业以变革谋未来助力企业以变革谋未来助力企业以变革谋未来助力企业以变革谋未来助力企业以变革谋未来助力企业以变革谋未来',
    cover: '/upload/img3.png',
    logo: '/upload/company-logo.png',
    url: '/',
  },
]
</script>

<template>
  <div class="report">
    <div class="p-t-64px container">
      <div class="text-28px lh-48px text-center">
        企业数字化诊断报告
      </div>
      <div class="text-16px text-#4B5B76 lh-48px m-t-8px flex gap-x-8px items-center justify-center">
        <RouterLink to="/consult?tab=1" class="text-primary m-l-8px flex items-center justify-center hover:opacity-50">
          <span>查看更多</span>
          <span class="i-carbon-chevron-right text-[0.9em] text-primary" />
        </RouterLink>
      </div>
      <NCarousel
        effect="card"
        :show-dots="false"
        show-arrow
        prev-slide-style="transform: translateX(-162%) translateZ(-150px); opacity: 1;"
        next-slide-style="transform: translateX(62%) translateZ(-150px); opacity: 1;"
        class="m-t-32px h-438px overflow-visible"
      >
        <template v-for="item in list" :key="item.url">
          <NCarouselItem>
            <div class="item b-rd-8px bg-white overflow-hidden">
              <div class="cover h-186px w-full">
                <img class="h-full w-full" :src="item.cover" alt="cover">
              </div>
              <div class="p-x-32px p-b-32px">
                <div class="logo p-y-3px b-rd-4px bg-white flex h-48px w-128px items-center justify-center overflow-hidden">
                  <img class="h-full w-auto" :src="item.logo" alt="logo">
                </div>
                <div class="text-20px lh-28px m-t--8px">
                  {{ item.title }}
                </div>
                <div class="text-16px text-#4B5B76 lh-28px m-t-8px">
                  {{ item.summary }}
                </div>
              </div>
            </div>
          </NCarouselItem>
        </template>
        <template #arrow="{ prev, next }">
          <div class="arrow">
            <NButton color="#fff" text-color="#98A3B7" class="arrow--left" round @click="prev">
              <template #icon>
                <span class="i-carbon:chevron-left text-[0.9em]" />
              </template>
            </NButton>
            <NButton color="#fff" text-color="#98A3B7" class="arrow--right" round @click="next">
              <template #icon>
                <span class="i-carbon:chevron-right text-[0.9em]" />
              </template>
            </NButton>
          </div>
        </template>
      </NCarousel>
    </div>
  </div>
</template>

<style scoped lang="less">
.report {
  background-color: #ffffff;
  .container {
    position: relative;
    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 0;
      z-index: 1;
      width: 30%;
      height: 100%;
      pointer-events: none;
    }
    &::before {
      left: 0;
      background-image: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0.95) 5%,
        rgba(255, 255, 255, 0.25) 50%,
        transparent
      );
    }
    &::after {
      right: 0;
      background-image: linear-gradient(
        270deg,
        rgba(255, 255, 255, 0.95) 5%,
        rgba(255, 255, 255, 0.25) 50%,
        transparent
      );
    }
    .arrow {
      opacity: 0;
      transition: all 0.2s ease;
      &--left,
      &--right {
        position: absolute;
        z-index: 9;
        top: 170px;
        width: 46px;
        height: 46px;
        box-shadow:
          0px 3px 12px 0px rgba(0, 0, 0, 0.07),
          0px 4px 12px 0px rgba(192, 199, 218, 0.3);
        &:hover {
          color: #0052d9;
        }
      }
      &--left {
        left: -64px;
      }
      &--right {
        right: -64px;
      }
    }
    &:hover {
      .arrow {
        opacity: 1;
      }
    }
  }
  :deep(.n-carousel__slides) {
    padding-bottom: 64px;
    overflow: hidden;
  }
  .n-carousel__slide {
    width: 588px;
    height: 374px;
    box-shadow: 0px 18px 36px 0px rgba(192, 199, 218, 0.3);
    .item {
      height: 100%;
      .cover {
        box-shadow: 0px 6px 12px 0px rgba(192, 199, 218, 0.5);
      }
      .logo {
        box-shadow: 0px 6px 12px 0px rgba(192, 199, 218, 0.5);
        position: relative;
        top: -24px;
      }
    }
  }
}
</style>
