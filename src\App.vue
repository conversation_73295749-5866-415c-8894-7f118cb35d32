<script setup lang="ts">
import type { GlobalThemeOverrides } from 'naive-ui'
import { dateZhCN, zhCN } from 'naive-ui'

const themeOverrides: GlobalThemeOverrides = {
  common: {
    primaryColor: '#0052D9',
    primaryColorHover: '#266FE8',
    primaryColorPressed: '#0343AE',
    primaryColorSuppl: '#D3E6FF',
    infoColor: '#02A5FF',
    infoColorHover: '#60C3FA',
    infoColorPressed: '#0870A9',
    infoColorSuppl: '#B3DDF4',
    successColor: '#00B42A',
    successColorHover: '#23C343',
    successColorPressed: '#009A29',
    successColorSuppl: '#AFF0B5',
    warningColor: '#FF7D00',
    warningColorHover: '#FF9A2E',
    warningColorPressed: '#D25F00',
    warningColorSuppl: '#FFE4BA',
    errorColor: '#F53F3F',
    errorColorHover: '#F76560',
    errorColorPressed: '#CB2634',
    errorColorSuppl: '#FDCDC5',
    textColorBase: '#181818',
    textColor1: '#4B5B76',
    textColor2: '#98A3B7',
    textColor3: '#CFD6E1',
  },
}
</script>

<template>
  <NConfigProvider :theme-overrides="themeOverrides" :locale="zhCN" :date-locale="dateZhCN" h-full>
    <AppProvider>
      <Header />
      <RouterView />
      <Footer />
    </AppProvider>
  </NConfigProvider>
</template>
