<script setup lang="ts">
defineOptions({
  name: 'ConsultService',
})

const serviceList = [
  {
    title: '战略规划',
    icon: '/assets/images/<EMAIL>',
    content: '战略环境洞察、战略顶层设计、战略规划及落地、战略管理体系；<p />产品战略、营销战略、研发战略、人力资源战略等专项战略',
  },
  {
    title: '组织及人力资源',
    icon: '/assets/images/<EMAIL>',
    content: '组织架构设计与优化、人力资源战略及规划、岗位体系设计、岗位价值评估、岗位职级与职业发展通道设计、薪酬体系设计与优化、绩效管理体系设计与优化',
  },
  {
    title: '企业数字化诊断',
    icon: '/assets/images/<EMAIL>',
    content: '对企业自身战略管控、组织流程、智能运营、商业创新、基础保障等维度进行诊断评估，并将诊断结果划分为多个模块及相应具体内容，供企业查看，推动企业建立涵盖数字化治理、组织机制、管理方式、组织文化、创新模式等的治理体系，为企业新型能力的建设、运行和优化提供机制保障',
  },
]

const pointList = [
  {
    title: '多样化·服务模式',
    icon: '/assets/images/<EMAIL>',
    content: '结合企业特点和承接能力，自由选择驾场式咨询、方案式咨询、顾问式咨询',
  },
  {
    title: '定制化·解决方案',
    icon: '/assets/images/<EMAIL>',
    content: '深度调研企业实际情况，因地制宜定制最适合的整休解决方案',
  },
  {
    title: '全方位·运作优势',
    icon: '/assets/images/<EMAIL>',
    content: '固定项目组服务客户，专业团队与企业充分沟通，计顶目进行更顺畅',
  },
  {
    title: '阶段化·落地实施',
    icon: '/assets/images/<EMAIL>',
    content: '将项目分解实施，提升方案的有效性和落地性',
  },
  {
    title: '数据化·实施结果',
    icon: '/assets/images/<EMAIL>',
    content: '在项目实施过程中，用数据验证项目改善效果，乃时修正',
  },
  {
    title: '终身制·售后服务',
    icon: '/assets/images/<EMAIL>',
    content: '为客户提供持续性跟讲服务，定期公开课让团队持续学习和和提升',
  },
]
</script>

<template>
  <div class="service m-x-24px p-y-16px">
    <div class="box">
      <div class="hd text-20px lh-28px font-medium">
        提供的咨询服务
      </div>
      <div v-for="item in serviceList" :key="item.title" class="bd m-t-24px">
        <div class="item p-26px">
          <div class="flex gap-x-8px items-center">
            <img class="h-20px w-20px" :src="item.icon" alt="">
            <span class="text-18px lh-25px font-medium">{{ item.title }}</span>
          </div>
          <div class="content text-16px text-#4B5B76 lh-28px m-t-10px" v-html="item.content" />
        </div>
      </div>
    </div>
    <div class="box m-t-40px">
      <div class="hd text-20px lh-28px font-medium">
        服务特点
      </div>
      <div class="bd m-t-24px">
        <div class="flex flex-wrap gap-y-24px items-center justify-between">
          <div v-for="item in pointList" :key="item.title" class="item p-32px b-rd-4px bg-#F5F7FA h-160px w-368px">
            <div class="flex gap-x-8px items-center">
              <img class="h-40px w-40px" :src="item.icon" alt="">
              <span class="text-20px lh-28px font-medium">{{ item.title }}</span>
            </div>
            <div class="content text-16px text-#4B5B76 lh-22px m-t-12px line-clamp-2" v-html="item.content" />
          </div>
        </div>
      </div>
    </div>
    <div class="box m-t-40px">
      <div class="hd text-20px lh-28px font-medium">
        服务流程
      </div>
      <div class="bd m-t-24px">
        <img class="w-full" src="/assets/images/<EMAIL>" alt="">
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.service {
  padding-bottom: 40px;
  .box {
    .bd {
      .item {
        background: #f5f7fa;
        box-shadow: 0px 4px 10px 0px rgba(192, 199, 218, 0.5);
        border-radius: 4px;
      }
    }
  }
}
</style>
