<script setup lang="ts">
import { useDictStore } from '~/stores'
import { showArea } from '~/utils/area'

defineOptions({
  name: 'IndustryDocDetail',
})

defineProps<{
  data: any
}>()

const useDict = useDictStore()

const imageBaseUrl = import.meta.env.VITE_STATIC_BASE_URL
</script>

<template>
  <div v-if="data" class="expert-detail">
    <div class="p-24px b-rd-4px bg-white">
      <div class="text-28px lh-40px">
        {{ data.name }}
      </div>
      <div class="m-t-24px flex justify-between">
        <div class="avatar b-rd-4px h-160px w-120px overflow-hidden">
          <img class="h-full w-full" :src="imageBaseUrl + data.avatar" alt="avatar">
        </div>
        <div class="flex flex-wrap w-1000px items-center overflow-hidden">
          <div class="w-25%">
            <span class="text-gray">年龄：</span>{{ data.age }}岁
          </div>
          <div class="w-25%">
            <span class="text-gray">学历：</span>{{ useDict.getLabel('Edu', data.edu) }}
          </div>
          <div class="w-25%">
            <span class="text-gray">是否党员：</span>{{ data.party ? '是' : '否' }}
          </div>
          <div class="w-25%">
            <span class="text-gray">工作经验：</span>{{ data.exp }}年
          </div>
          <div class="w-25%">
            <span class="text-gray">常住地：</span>{{ showArea(data.area).join('-') }}
          </div>
          <div class="w-25%">
            <span class="text-gray">联系电话：</span>{{ data.phone }}
          </div>
          <div class="w-25%">
            <span class="text-gray">邮箱：</span>{{ data.email }}
          </div>
          <div class="w-50%">
            <span v-if="data.type && data.type.length > 0" class="text-gray">擅长领域：</span>{{ data.type.map((item: any) => useDict.getLabel('ExpertType', item)).join('，') }}
          </div>
          <div class="w-50%">
            <span v-if="data.skills && data.skills.length > 0" class="text-gray">专业技能：</span>{{ data.skills.join('，') }}
          </div>
        </div>
      </div>
      <div class="m-y-24px b b-t-1px b-color-gray-100" />
      <div class="detail min-h-50vh prose" v-html="data.detail" />
    </div>
  </div>
</template>

<style scoped lang="less"></style>
