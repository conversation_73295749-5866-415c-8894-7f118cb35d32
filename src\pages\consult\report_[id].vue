<script setup lang="ts">
import { baseApi } from '~/service/modules/base'
import Detail from './modules/detail.vue'

defineOptions({
  name: 'ConsultReportPage',
})

const route = useRoute()

const detail = ref<any>()

async function getData() {
  const { id } = route.params as { id: string }
  const res = await baseApi.reportDetail(id)
  if (res.code === 200) {
    detail.value = res.data
  }
}

onMounted(() => {
  getData()
})
</script>

<template>
  <main>
    <NBreadcrumb separator=">" class="text-16px p-t-16px container">
      <NBreadcrumbItem href="/" class="flex gap-x-4px items-end">
        <div class="flex gap-x-4px items-end">
          <i class="i-carbon-home" />
          <span>首页</span>
        </div>
      </NBreadcrumbItem>
      <NBreadcrumbItem @click="$router.back()">
        企业管理咨询
      </NBreadcrumbItem>
      <NBreadcrumbItem>
        报告详情
      </NBreadcrumbItem>
    </NBreadcrumb>
    <div class="p-y-16px container">
      <Detail :data="detail" />
    </div>
  </main>
</template>

<style scoped lang="less">

</style>
