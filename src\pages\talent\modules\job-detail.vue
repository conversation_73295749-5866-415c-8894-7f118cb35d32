<script setup lang="ts">
import { baseApi } from '~/service/modules/base'

defineOptions({
  name: 'TalentJobDetail',
})

const props = defineProps<{
  data: any
}>()

const company = ref<any>({
  contact: '',
  phone: '',
  email: '',
  address: '',
  logo: '',
  name: '',
  tags: [],
  detail: '',
})

async function getCompany() {
  if (!props.data?.companyId) {
    return
  }

  const res = await baseApi.companyDetail(props.data.companyId)
  if (res.code === 200) {
    company.value = res.data
  }
}

watch(() => props.data, () => {
  getCompany()
}, { immediate: true })

const showContact = ref(false)
</script>

<template>
  <div v-if="data" class="job-detail">
    <div class="info p-24px b-rd-4px bg-white flex items-center justify-between">
      <div class="flex flex-1 items-center">
        <div class="text-28px lh-40px font-medium flex gap-x-16px">
          <span>{{ data.title }}</span>
          <span class="text-#E70B0B">{{ data.salary[0] / 1000 }}~{{ data.salary[1] / 1000 }}K</span>
        </div>
        <div class="m-l-24px flex gap-x-8px">
          <NTag v-for="tag in data.tags" :key="tag" :color="{ color: '#F1F2F5', textColor: '#4B5B76' }" size="small" :bordered="false">
            {{ tag }}
          </NTag>
        </div>
      </div>
      <NButton v-if="company.contact" type="primary" ghost size="large" class="text-18px font-bold h-56px w-150px" @click="showContact = true">
        联系企业
      </NButton>
      <NModal
        v-model:show="showContact"
        class="w-500px"
        preset="card"
        title="联系方式"
        size="huge"
        :bordered="false"
      >
        <ul class="text-black p-x-16px flex flex-col gap-y-16px">
          <li><span class="text-gray w-100px inline-block">联系人：</span>{{ company.contact }}</li>
          <li><span class="text-gray w-100px inline-block">联系电话：</span>{{ company.phone }}</li>
          <li><span class="text-gray w-100px inline-block">企业邮箱：</span>{{ company.email }}</li>
        </ul>
      </NModal>
    </div>
    <div class="detail text-16px text-#4B5B76 lh-28px m-t-24px p-24px bg-white">
      <div class="hd">
        职位信息
      </div>
      <div class="bd prose" v-html="data.detail" />
      <div class="hd">
        工作地点
      </div>
      <div class="bd">
        上班地点：{{ company.address }}
      </div>
      <div class="hd">
        公司介绍
      </div>
      <div class="bd">
        <div class="flex gap-x-8px">
          <NTag v-for="tag in company.tags" :key="tag" :color="{ color: '#F1F2F5', textColor: '#4B5B76' }" size="small" :bordered="false">
            {{ tag }}
          </NTag>
        </div>
        <div class="m-t-16px prose" v-html="company.detail" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.job-detail {
  .info {
    box-shadow: 0px 18px 36px 0px rgba(192, 199, 218, 0.3);
  }
  .detail {
    .hd {
      font-size: 20px;
      line-height: 1;
      font-weight: bold;
      color: #181818;
      border-left: 3px solid #0052d9;
      padding-left: 16px;
    }
    .bd {
      padding: 18px 0;
      &.prose {
        max-width: 100%;
      }
    }
  }
}
</style>
