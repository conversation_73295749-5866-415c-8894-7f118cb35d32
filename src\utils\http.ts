import type { AxiosInstance, AxiosRequestConfig, AxiosRequestHeaders, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import axios from 'axios'
import { useAuthStore, useGlobalStore } from '~/stores'

const StatusCodeMessage: Api.CodeMessage = {
  200: '服务器成功返回请求的数据',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）',
  204: '删除数据成功',
  400: '请求错误(400)',
  401: '未授权，请重新登录(401)',
  403: '拒绝访问(403)',
  404: '请求出错(404)',
  408: '请求超时(408)',
  500: '服务器错误(500)',
  501: '服务未实现(501)',
  502: '网络错误(502)',
  503: '服务不可用(503)',
  504: '网络超时(504)',
}

const config = {
  // 默认地址请求地址，可在 .env 开头文件中修改
  baseURL: import.meta.env.MODE === 'development' ? '/proxy' : import.meta.env.VITE_SERVICE_BASE_URL,
  // 设置超时时间（10s）
  timeout: 10 * 1000,
  // 跨域时候允许携带凭证
  // withCredentials: true,
  headers: {
    // 'X-App-Code': 'cockpit',
  },
}

// 实例化axios
const http: AxiosInstance = axios.create(config)

// 请求拦截器
http.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const globalStore = useGlobalStore()
    const authStore = useAuthStore()

    globalStore.showLoading()
    const token = authStore.getToken()
    if (token) {
      if (!config.headers)
        config.headers = <AxiosRequestHeaders>{}

      config.headers.Authorization = token
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse) => {
    const globalStore = useGlobalStore()
    const authStore = useAuthStore()

    const { data } = response
    const { msg, error, code } = data

    // 无效授权
    if (code === 401) {
      globalStore.hideLoading()
      // 清理token
      authStore.clearToken()
      window.$message?.error(msg)

      return Promise.reject(new Error(StatusCodeMessage[response.status]))
    }

    if (error) {
      globalStore.hideLoading()
      window.$message?.error(msg)

      return Promise.reject(new Error(StatusCodeMessage[response.status]))
    }

    globalStore.hideLoading()
    return response
  },
  (error) => {
    const globalStore = useGlobalStore()
    const authStore = useAuthStore()

    globalStore.hideLoading()
    window.$message?.destroyAll()

    // 无效授权 hack kersen
    if (error.status === 401) {
      globalStore.hideLoading()
      // 清理token
      authStore.clearToken()
      window.$message?.error('无效授权, 请重新登录！')

      return Promise.reject(error)
    }

    const response = Object.assign({}, error.response)
    if (response) {
      window.$message?.error(StatusCodeMessage[response.status] || '系统异常, 请检查网络或联系管理员！')
    }
    return Promise.reject(error)
  },
)

export function request<T = unknown>(config: AxiosRequestConfig): Promise<Api.ResultData<T>> {
  return new Promise((resolve, reject) => {
    http
      .request<T>(config)
      .then((res: AxiosResponse) => resolve(res.data))
      .catch((err: { message: string }) => reject(err))
  })
}
