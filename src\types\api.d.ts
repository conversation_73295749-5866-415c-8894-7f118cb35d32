declare namespace Api {
  interface CodeMessage {
    [propName: number]: string
  }

  interface ResultData<T> {
    code: number
    data: T
    message: string
  }

  // 授权登录
  namespace Auth {
    interface LoginParams {
      username: string
      password: string
    }

    interface LoginResponse {
      accessToken: string
    }
  }

  // 基础数据
  namespace Base {
    // 配置数据结构
    interface Config {
      name: string
      code: string
      params: ConfigParam[]
    }

    interface ConfigParam {
      name: string
      code: string
      value: any
      status: bool
    }

    // 字典项
    interface Dict {
      name: string
      code: string
      options: DictOption[]
    }

    interface DictOption {
      label: string
      value: any
      status: bool
    }
  }
}
