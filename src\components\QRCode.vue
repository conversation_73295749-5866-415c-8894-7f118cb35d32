<script setup lang="ts">
import jrQrcode from 'jr-qrcode'
import { onMounted, ref } from 'vue'

const props = defineProps({
  value: {
    type: String,
    default: () => '',
  },
  options: {
    type: Object,
    default: () => ({}),
  },
})

const imgBase64 = ref<string>('')

watch(() => props.value, (nval) => {
  imgBase64.value = jrQrcode.getQrBase64(nval, props.options)
})

watch(() => props.options, (nval) => {
  imgBase64.value = jrQrcode.getQrBase64(props.value, nval)
})

onMounted(() => {
  imgBase64.value = jrQrcode.getQrBase64(props.value, props.options)
})
</script>

<template>
  <img :src="imgBase64">
</template>

<style></style>
