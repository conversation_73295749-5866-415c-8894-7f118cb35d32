<script setup lang="ts">
defineOptions({
  name: 'HomeBanner',
})

defineProps<{
  data: any[] | undefined
}>()

const imageBaseUrl = import.meta.env.VITE_STATIC_BASE_URL
</script>

<template>
  <div class="banner flex justify-center">
    <NCarousel :draggable="data && data.length > 1" autoplay>
      <template v-for="item in data" :key="item.id">
        <NCarouselItem :style="{ backgroundImage: `url('${imageBaseUrl + item.cover}')` }">
          <div class="h-400px container">
            <div class="text-42px lh-62px font-bold m-t-76px">
              {{ item.title }}
            </div>
            <div class="text-16px text-#4B5B76 lh-24px m-t-24px w-45%">
              {{ item.summary }}
            </div>
            <NButton type="primary" size="large" class="m-t-48px w-148px" @click="$router.push(`/post/${item.id}`)">
              了解详情
            </NButton>
          </div>
        </NCarouselItem>
      </template>
      <template #dots="{ total, currentIndex, to }">
        <ul class="dots m-0 p-0 flex items-center justify-center">
          <li
            v-for="index of total"
            :key="index"
            :class="{ ['active']: currentIndex === index - 1 }"
            @click="to(index - 1)"
          />
        </ul>
      </template>
    </NCarousel>
  </div>
</template>

<style scoped lang="less">
.banner {
  height: 400px;
  background-color: #f0f2fb;
  .n-carousel__slide {
    background-position: center;
    background-size: auto 400px;
  }
  .dots {
    position: absolute;
    bottom: 20px;
    left: 0;
    width: 100%;
    li {
      display: inline-block;
      width: 24px;
      height: 4px;
      margin: 0 3px;
      background-color: #d7dbdf;
      transition:
        width 0.3s,
        background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;

      &.active {
        width: 48px;
        background: #0052d9;
      }
    }
  }
}
</style>
