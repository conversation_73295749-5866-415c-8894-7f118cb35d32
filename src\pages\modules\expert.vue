<script setup lang="ts">
defineOptions({
  name: 'HomeEx<PERSON>',
})

const list = [
  {
    name: '安健',
    avatar: '/upload/avatar1.png',
    url: '/',
  },
  {
    name: '安健',
    avatar: '/upload/avatar2.png',
    url: '/',
  },
  {
    name: '安健',
    avatar: '/upload/avatar3.png',
    url: '/',
  },
  {
    name: '安健',
    avatar: '/upload/avatar4.png',
    url: '/',
  },
  {
    name: '安健',
    avatar: '/upload/avatar1.png',
    url: '/',
  },
  {
    name: '安健',
    avatar: '/upload/avatar2.png',
    url: '/',
  },
  {
    name: '安健',
    avatar: '/upload/avatar3.png',
    url: '/',
  },
  {
    name: '安健',
    avatar: '/upload/avatar4.png',
    url: '/',
  },
]
</script>

<template>
  <div class="expert">
    <div class="p-y-64px overflow-hidden container">
      <div class="text-28px lh-48px text-center">
        行业专家
      </div>
      <div class="text-16px text-#4B5B76 lh-48px m-t-8px flex gap-x-8px items-center justify-center">
        专业赋能，驱动持续增长
      </div>
      <NTabs type="segment" class="m-t-32px">
        <NTabPane :name="1" tab="行业专家">
          <NCarousel :slides-per-view="4" :space-between="23" :loop="false" draggable class="overflow-visible">
            <template v-for="item in list" :key="item.url">
              <NCarouselItem>
                <div class="item cursor-pointer">
                  <div class="avatar">
                    <img :src="item.avatar" alt="avatar">
                  </div>
                  <div class="name text-20px text-white lh-56px text-center">
                    {{ item.name }}
                  </div>
                </div>
              </NCarouselItem>
            </template>
            <template #dots="{ total, currentIndex, to }">
              <ul class="dots m-0 p-0 flex items-center justify-center">
                <li
                  v-for="index of total"
                  :key="index"
                  :class="{ ['active']: currentIndex === index - 1 }"
                  @click="to(index - 1)"
                />
              </ul>
            </template>
          </NCarousel>
        </NTabPane>
        <NTabPane :name="2" tab="管理专家">
          <NCarousel :slides-per-view="4" :space-between="23" :loop="false" draggable class="overflow-visible">
            <template v-for="item in list" :key="item.url">
              <NCarouselItem>
                <div class="item cursor-pointer">
                  <div class="avatar">
                    <img :src="item.avatar" alt="avatar">
                  </div>
                  <div class="name text-20px text-white lh-56px text-center">
                    {{ item.name }}
                  </div>
                </div>
              </NCarouselItem>
            </template>
            <template #dots="{ total, currentIndex, to }">
              <ul class="dots m-0 p-0 flex items-center justify-center">
                <li
                  v-for="index of total"
                  :key="index"
                  :class="{ ['active']: currentIndex === index - 1 }"
                  @click="to(index - 1)"
                />
              </ul>
            </template>
          </NCarousel>
        </NTabPane>
      </NTabs>
    </div>
  </div>
</template>

<style scoped lang="less">
.expert {
  background: url('/assets/images/<EMAIL>') no-repeat center center / cover;
  :deep(.n-tabs-nav) {
    width: 320px;
    margin: 0 auto;
    box-shadow: 0px 6px 12px 0px rgba(192, 199, 218, 0.5);
    .n-tabs-tab__label {
      color: #4b5b76;
    }
    .n-tabs-tab--active {
      .n-tabs-tab__label {
        color: #fff;
      }
    }
    .n-tabs-capsule {
      background: #0052d9;
    }
  }
  :deep(.n-carousel__slides) {
    height: 430px;
  }
  .n-carousel__slide {
    padding-top: 32px;
    .item {
      position: relative;
      height: 398px;
      border-radius: 4px;
      overflow: hidden;
      transition: all 0.2s ease-in-out;
      &:hover {
        transform: translateY(-16px);
      }
      .avatar img {
        height: 398px;
        width: 100%;
      }
      .name {
        position: absolute;
        left: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.6);
        width: 100%;
        height: 56px;
      }
    }
  }
  .dots {
    position: absolute;
    bottom: -32px;
    left: 0;
    width: 100%;
    li {
      display: inline-block;
      width: 24px;
      height: 4px;
      margin: 0 3px;
      background-color: #d7dbdf;
      transition:
        width 0.3s,
        background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      cursor: pointer;

      &.active {
        width: 48px;
        background: #0052d9;
      }
    }
  }
}
</style>
