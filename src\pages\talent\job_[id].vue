<script setup lang="ts">
import { baseApi } from '~/service/modules/base'
import Detail from './modules/job-detail.vue'

defineOptions({
  name: 'TalentJobPage',
})

const route = useRoute()

const detail = ref<any>()

async function getData() {
  const { id } = route.params as { id: string }
  const res = await baseApi.jobDetail(id)
  if (res.code === 200) {
    detail.value = res.data
  }
}

onMounted(() => {
  getData()
})
</script>

<template>
  <main>
    <NBreadcrumb separator=">" class="text-16px p-t-16px container">
      <NBreadcrumbItem href="/" class="flex gap-x-4px items-end">
        <div class="flex gap-x-4px items-end">
          <i class="i-carbon-home" />
          <span>首页</span>
        </div>
      </NBreadcrumbItem>
      <NBreadcrumbItem @click="$router.back()">
        企业人才管理
      </NBreadcrumbItem>
      <NBreadcrumbItem>
        岗位详情
      </NBreadcrumbItem>
    </NBreadcrumb>
    <div class="p-y-16px container">
      <Detail :data="detail" />
    </div>
  </main>
</template>

<style scoped lang="less">
.container {
  :deep(.n-tabs) {
    --n-tab-gap: 48px !important;
    .n-tabs-nav {
      background-color: #fff;
      .n-tabs-wrapper {
        padding: 8px 24px;
        .n-tabs-tab__label {
          color: #4b5b76;
          font-size: 20px;
          line-height: 28px;
        }
        .n-tabs-tab--active {
          .n-tabs-tab__label {
            color: #181818;
            font-weight: bold;
          }
        }
      }
    }
    .n-tabs-pane-wrapper {
      overflow: visible;
    }
    .n-tab-pane {
      --n-pane-text-color: #181818;
      padding: 0;
    }
  }
}
</style>
