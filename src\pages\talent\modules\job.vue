<script setup lang="ts">
import { baseApi } from '~/service/modules/base'
import { useDictStore } from '~/stores'

defineOptions({
  name: 'TalentJob',
})

const useDict = useDictStore()

const imageBaseUrl = import.meta.env.VITE_STATIC_BASE_URL

const list = ref<any>()

const type = ref(0)

const filter = ref({
  _page: 1,
  _limit: 20,
  _expand: 'company',
  q: '',
  type,
})

async function getData() {
  const res = await baseApi.jobList(filter.value)
  if (res.code === 200) {
    list.value = res.data
  }
}

onMounted(() => {
  getData()
})

watch(type, () => getData())
</script>

<template>
  <div class="job">
    <div class="search p-24px bg-white">
      <div class="keyword p-x-32px b-rd-4px flex h-128px w-full items-center">
        <div class="text-white w-40%">
          <div class="text-28px lh-40px">
            人才需求
          </div>
          <div class="text-16px lh-22px m-t-8px opacity-80">
            八方来「 才」，一「 招」即中！
          </div>
        </div>
        <NInputGroup class="w-60%">
          <NInput v-model:value="filter.q" size="large" placeholder="请输入关键词" clearable>
            <template #prefix>
              <span class="i-carbon-search text-16px" />
            </template>
          </NInput>
          <NButton size="large" type="info" class="w-104px" @click="getData">
            查询
          </NButton>
        </NInputGroup>
      </div>
      <div class="text-18px lh-25px m-t-24px filter">
        <div class="flex items-center justify-between">
          <div class="label text-#4B5B76 text-right w-120px">
            岗位类型：
          </div>
          <div class="w-1000px">
            <NRadioGroup v-model:value="type">
              <NRadioButton :key="0" :value="0">
                全部
              </NRadioButton>
              <NRadioButton v-for="item in useDict.get('JobType')" :key="item.value" :value="item.value">
                {{ item.label }}
              </NRadioButton>
            </NRadioGroup>
          </div>
        </div>
      </div>
    </div>
    <div class="list m-t-24px">
      <div v-if="list?.records.length > 0" class="flex flex-col gap-24px">
        <RouterLink v-for="item in list?.records" :key="item.id" :to="`/talent/job_${item.id}`" class="item b-rd-4px overflow-hidden">
          <div class="info p-x-24px p-t-16px flex items-center justify-between">
            <div class="flex flex-1 items-center">
              <div class="text-28px lh-40px font-medium flex gap-x-16px">
                <span>{{ item.title }}</span>
                <span v-if="item.salaryType === 1" class="text-#E70B0B">{{ item.salary[0] / 1000 }}~{{ item.salary[1] / 1000 }}K</span>
                <span v-else class="text-#E70B0B">面议</span>
              </div>
              <div class="m-l-24px flex gap-x-8px">
                <NTag v-for="tag in item.tags" :key="item.id + tag" :color="{ color: '#F1F2F5', textColor: '#4B5B76' }" size="small" :bordered="false">
                  {{ tag }}
                </NTag>
              </div>
            </div>
            <NButton type="primary" ghost size="large" class="text-18px font-bold h-56px w-150px">
              查看详情
            </NButton>
          </div>
          <div class="company m-t-16px p-24px flex gap-x-16px items-center">
            <img class="h-48px w-auto" :src="imageBaseUrl + item.company.logo" alt="cover">
            <div class="company-info">
              <div class="text-16px lh-22px font-medium">
                {{ item.company.name }}
              </div>
              <div class="text-14px text-#98A3B7 lh-20px m-t-6px">
                {{ item.company.tags.join(' | ') }}
              </div>
            </div>
          </div>
        </RouterLink>
      </div>
      <NEmpty v-else size="large" description="暂无数据" class="p-y-120px" />
    </div>
    <div class="m-t-36px flex items-center justify-center">
      <NPagination
        v-model:page="filter._page"
        :page-count="list?.total"
        :page-size="filter._limit"
        size="large"
        show-quick-jumper
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.job {
  padding-bottom: 40px;
  .search {
    .keyword {
      background: url('/assets/images/<EMAIL>') no-repeat center center / cover;
      :deep(.n-input) {
        --n-height: 56px !important;
        --n-color: #f5f7fa !important;
        --n-border: #f5f7fa !important;
        --n-font-size: 16px !important;
      }
      :deep(.n-button) {
        --n-height: 56px !important;
      }
    }
    .filter {
      .line {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f5f7fa;
      }
      :deep(.n-radio-group) {
        display: flex;
        gap: 0 24px;
        .n-radio-group__splitor,
        .n-radio-button__state-border {
          display: none;
        }
        .n-radio-button {
          border: 0;
          border-radius: 4px;
          font-size: 18px;
          color: #181818;
        }
        .n-radio-button--checked {
          background: #ccdeff;
          color: #0052d9;
        }
      }
    }
  }
  .list {
    .item {
      background: #ffffff;
      box-shadow: 0px 18px 36px 0px rgba(192, 199, 218, 0.3);
      transition: all 0.3s ease-in-out;
      .company {
        transition: all 0.3s ease-in-out;
        background: url('/assets/images/<EMAIL>') no-repeat center center / 500% 500%;
      }
      &:hover {
        transform: translateY(-10px);
        box-shadow: 0px 18px 36px 0px rgba(192, 199, 218, 0.5);
        .company {
          background-size: 100% 100%;
        }
      }
    }
  }
}
</style>
