import { areaList } from '@vant/area-data'

export function showArea(code: string) {
  const codes = code.match(/.{1,2}/g) || ['11', '00', '00']

  const areas = [areaList.province_list[`${codes[0]}0000`]]

  if (codes[1] !== '00') {
    areas.push(areaList.city_list[`${codes[0] + codes[1]}00`])
  }
  if (codes[2] !== '00') {
    areas.push(areaList.county_list[code])
  }

  return [...new Set(areas)]
}
