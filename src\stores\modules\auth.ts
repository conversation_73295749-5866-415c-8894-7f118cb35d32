import { acceptHMRUpdate, defineStore } from 'pinia'
import { ref } from 'vue'
import { useRouter } from 'vue-router'

interface userInter {
  uerId: number
  username: string
  nickname: string
  roleIds: number[]
  company: any
}

export const useAuthStore = defineStore(
  'auth',
  () => {
    // 路由
    const router = useRouter()

    // 登录token
    const token = ref(localStorage.getItem('token') || '')

    // 用户信息
    const userinfo = ref<userInter>()

    // 保存token
    function setToken(param: string) {
      token.value = param
      localStorage.setItem('token', param)
    }

    // 格式化token
    function getToken() {
      return token.value ? `Bearer ${token.value}` : null
    }

    // 清除token
    function clearToken() {
      token.value = ''
      localStorage.removeItem('token')
      router.replace('/login')
    }

    // 设置用户信息
    function setUser(param: userInter) {
      userinfo.value = param
    }

    return {
      userinfo,
      setToken,
      getToken,
      clearToken,
      setUser,
    }
  },
  {
    persist: {
      storage: localStorage,
      pick: ['token'],
    },
  },
)

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useAuthStore as any, import.meta.hot))
