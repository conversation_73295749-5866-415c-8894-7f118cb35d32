<script setup lang="ts">
const menus = [
  {
    name: '首页',
    path: '/',
  },
  // {
  //   name: '数字互联平台',
  //   path: '/cloud',
  // },
  {
    name: '企业管理咨询',
    path: '/consult',
  },
  {
    name: '行业知识库',
    path: '/industry',
  },
  {
    name: '企业人才管理',
    path: '/talent',
  },
  {
    name: '企业库',
    path: '/company',
  },
]
</script>

<template>
  <div class="header flex h-80px justify-center">
    <div class="flex h-full w-1360px items-center justify-between">
      <div class="logo">
        <RouterLink cursor-pointer to="/">
          <img class="h-54px" src="/assets/images/<EMAIL>" alt="logo">
        </RouterLink>
      </div>
      <div class="flex h-full w-800px items-center justify-between">
        <template v-for="menu in menus" :key="menu.path">
          <RouterLink cursor-pointer :to="menu.path" class="item text-20px lh-80px h-full" active-class="active">
            {{ menu.name }}
          </RouterLink>
        </template>
      </div>
    </div>
  </div>
  <div class="header-placeholder h-80px" />
</template>

<style scoped lang="less">
.header {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  background-color: #fff;
  box-shadow: 0px 4px 10px 0px rgba(192, 199, 218, 0.2);

  .item {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 4px;
      transition: all 0.3s ease-in-out;
    }
    &:hover {
      &::after {
        background-color: #d3e6ff;
      }
    }
    &.active {
      &::after {
        background-color: #0052d9;
      }
    }
  }
}
</style>
