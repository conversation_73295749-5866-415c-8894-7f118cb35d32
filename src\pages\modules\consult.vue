<script setup lang="ts">
defineOptions({
  name: 'HomeConsult',
})

const list = [
  {
    title: '战略规划',
    summary: '明晰发展路径，锚定目标实现阶梯',
    cover: '/assets/images/<EMAIL>',
    icon: '/assets/images/<EMAIL>',
  },
  {
    title: '组织及人力资源',
    summary: '构建敏捷架构，激活人才价值动能',
    cover: '/assets/images/<EMAIL>',
    icon: '/assets/images/<EMAIL>',
  },
  {
    title: '企业数字化诊断',
    summary: '透视转型痛点，解码数智跃迁路径',
    cover: '/assets/images/<EMAIL>',
    icon: '/assets/images/<EMAIL>',
  },
]
</script>

<template>
  <div class="consult">
    <div class="p-y-64px container">
      <div class="text-28px lh-48px text-center">
        企业管理咨询
      </div>
      <div class="text-16px text-#4B5B76 lh-48px m-t-8px flex gap-x-8px items-center justify-center">
        助力企业以变革谋未来
        <RouterLink to="/consult?tab=0" class="text-primary m-l-8px flex items-center justify-center hover:opacity-50">
          <span>了解详情</span>
          <span class="i-carbon-chevron-right text-[0.9em] text-primary" />
        </RouterLink>
      </div>
      <div class="m-t-32px flex items-center justify-between">
        <div v-for="(item, index) in list" :key="index" class="item p-y-32px flex flex-col w-384px items-center justify-center" :style="{ backgroundImage: `url('${item.cover}')` }">
          <img :src="item.icon" alt="icon">
          <div class="text-bold text-20px lh-28px">
            {{ item.title }}
          </div>
          <div class="text-16px text-#4B5B76 lh-28px m-t-8px">
            {{ item.summary }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.consult {
  background-color: #f5f7fa;
  .item {
    background-position: center;
    background-size: 100% 100%;
    border-radius: 8px;
    box-shadow: 0px 18px 36px 0px rgba(192, 199, 218, 0.3);
    transition: all 0.2s ease-in-out;
    &:hover {
      background-size: 200% 200%;
    }
    img {
      width: 90px;
      height: 88px;
    }
  }
}
</style>
