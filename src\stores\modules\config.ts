import { acceptHMRUpdate, defineStore } from 'pinia'
import { ref } from 'vue'
import { baseApi } from '~/service/modules/base'

export const useConfigStore = defineStore(
  'config',
  () => {
    // 全局配置数据
    const config = ref<Api.Base.Config[]>()

    // 获取配置数据
    async function init() {
      if (config.value) {
        return
      }
      try {
        const res = await baseApi.config()
        if (res.code === 200) {
          config.value = res.data
        }
      }
      catch (error) {
        console.error('获取配置数据失败:', error)
      }
    }

    // 自动初始化
    init()

    // 根据配置编码获取配置项
    function get(code: string) {
      return config.value?.find(item => item.code === code)?.params || []
    }

    // 根据配置项编码获取配置项的值
    function getValue(code: string, paramCode: string) {
      return get(code).find(item => item.code === paramCode)?.value || paramCode
    }

    return {
      config,
      init,
      get,
      getValue,
    }
  },
  {
    persist: {
      storage: localStorage,
      pick: ['config'],
    },
  },
)

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useConfigStore as any, import.meta.hot))
