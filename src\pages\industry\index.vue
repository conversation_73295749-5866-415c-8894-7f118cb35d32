<script setup lang="ts">
import Ai from './modules/ai.vue'
import Doc from './modules/doc.vue'
import Expert from './modules/expert.vue'

defineOptions({
  name: 'IndustryPage',
})

const route = useRoute()
const router = useRouter()

const active = ref(route.query.tab ? Number(route.query.tab) : 0)

watch(active, (val) => {
  router.replace({ query: { ...route.query, tab: val } })
})
</script>

<template>
  <main>
    <NBreadcrumb separator=">" class="text-16px p-t-16px container">
      <NBreadcrumbItem href="/" class="flex gap-x-4px items-end">
        <div class="flex gap-x-4px items-end">
          <i class="i-carbon-home" />
          <span>首页</span>
        </div>
      </NBreadcrumbItem>
      <NBreadcrumbItem>
        行业知识库
      </NBreadcrumbItem>
    </NBreadcrumb>
    <div class="p-y-16px flex justify-between container">
      <NTabs v-model:value="active" type="line" :class="active === 0 ? 'w-792px' : 'w-full'">
        <NTabPane :name="0" tab="行业知识库">
          <Doc />
        </NTabPane>
        <NTabPane :name="1" tab="行业专家">
          <Expert />
        </NTabPane>
      </NTabs>
      <div class="side overflow-hidden" :class="active === 0 ? 'w-384px' : 'w-0 opacity-0'">
        <Ai />
      </div>
    </div>
  </main>
</template>

<style scoped lang="less">
.container {
  :deep(.n-tabs) {
    --n-tab-gap: 48px !important;
    transition: width 0.2s ease-in-out;
    .n-tabs-nav {
      background-color: #fff;
      .n-tabs-wrapper {
        padding: 8px 24px;
        .n-tabs-tab__label {
          color: #4b5b76;
          font-size: 20px;
          line-height: 28px;
        }
        .n-tabs-tab--active {
          .n-tabs-tab__label {
            color: #181818;
            font-weight: bold;
          }
        }
      }
    }
    .n-tabs-pane-wrapper {
      overflow: visible;
    }
    .n-tab-pane {
      --n-pane-text-color: #181818;
      padding: 0;
    }
  }
  .side {
    transition: width 0.2s ease-in-out;
  }
}
</style>
