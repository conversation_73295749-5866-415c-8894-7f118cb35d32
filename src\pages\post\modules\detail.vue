<script setup lang="ts">
import dayjs from 'dayjs'

defineOptions({
  name: 'ConsultReportDetail',
})

defineProps<{
  data: any
}>()

const imageBaseUrl = import.meta.env.VITE_STATIC_BASE_URL
</script>

<template>
  <div v-if="data" class="report-detail">
    <div class="p-24px b-rd-4px bg-white">
      <div class="text-28px lh-40px">
        {{ data.title }}
      </div>
      <div class="m-t-24px flex gap-x-24px items-center overflow-hidden">
        <div><span class="text-gray">日期：</span>{{ dayjs(data.createdAt).format('YYYY年MM月DD日') }}</div>
        <div><span v-if="data.tags && data.tags.length > 0" class="text-gray">标签：</span>{{ data.tags.join('，') }}</div>
      </div>
      <div class="m-y-24px b b-t-1px b-color-gray-100" />
      <div class="detail min-h-50vh prose" v-html="data.content" />
      <div v-if="data.files && data.files.length > 0" class="attach m-t-16px p-16px bg-gray-100">
        <h4>附件：</h4>
        <ul class="m-t-8px">
          <li v-for="(item, index) in data.files" :key="index">
            <a class="text-primary hover:opacity-80" :href="imageBaseUrl + item.url" target="_blank">《{{ item.name }}》</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less"></style>
