<script setup lang="ts">
import { baseApi } from '~/service/modules/base'

defineOptions({
  name: 'ConsultReport',
})

const imageBaseUrl = import.meta.env.VITE_STATIC_BASE_URL

const list = ref<any>()

const filter = ref({
  _page: 1,
  _limit: 20,
  q: '',
})

async function getData() {
  const res = await baseApi.reportList(filter.value)
  if (res.code === 200) {
    list.value = res.data
  }
}

onMounted(() => {
  getData()
})
</script>

<template>
  <div class="report m-x-24px p-y-16px">
    <div class="search">
      <NInputGroup>
        <NInput v-model:value="filter.q" size="large" placeholder="请输入关键词" clearable>
          <template #prefix>
            <span class="i-carbon-search text-16px" />
          </template>
        </NInput>
        <NButton size="large" type="primary" class="w-104px" @click="getData">
          查询
        </NButton>
      </NInputGroup>
    </div>
    <div class="list m-t-24px">
      <div v-if="list?.records.length > 0" class="flex flex-wrap gap-24px">
        <RouterLink v-for="item in list?.records" :key="item.title" :to="`/consult/report_${item.id}`" class="item p-26px b-rd-4px hover:text-primary">
          <div class="p-y-16px flex flex-col gap-y-16px items-center justify-center">
            <img class="h-112px w-112px" :src="imageBaseUrl + item.cover" alt="">
            <span class="text-18px lh-25px font-medium">{{ item.title }}</span>
          </div>
        </RouterLink>
      </div>
      <NEmpty v-else size="large" description="暂无数据" class="p-y-120px" />
    </div>
    <div class="m-t-36px flex items-center justify-center">
      <NPagination
        v-model:page="filter._page"
        :page-count="list?.total"
        :page-size="filter._limit"
        size="large"
        show-quick-jumper
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.report {
  padding-bottom: 40px;
  .search {
    :deep(.n-input) {
      --n-height: 56px !important;
      --n-color: #f5f7fa !important;
      --n-border: #f5f7fa !important;
      --n-font-size: 16px !important;
    }
    :deep(.n-button) {
      --n-height: 56px !important;
    }
  }
  .list {
    .item {
      background: #f5f7fa;
      width: 270px;
      height: 242px;
      transition: all 0.3s ease-in-out;
      &:hover {
        transform: translateY(-10px);
        box-shadow: 0px 18px 36px 0px rgba(192, 199, 218, 0.5);
      }
    }
  }
}
</style>
