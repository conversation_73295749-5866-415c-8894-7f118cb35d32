<script setup lang="ts">
defineOptions({
  name: 'HomeCloud',
})

defineProps<{
  data: any[] | undefined
}>()

function openLink(url: string) {
  window.open(url, '_blank')
}
</script>

<template>
  <div class="cloud">
    <div class="p-y-64px container">
      <div class="text-28px lh-48px text-center">
        工业互联网平台
      </div>
      <div class="text-16px text-#4B5B76 lh-48px m-t-8px flex gap-x-8px items-center justify-center">
        应用市场
      </div>
      <NTabs v-if="data" type="line" placement="left" animated class="m-t-32px">
        <template v-for="(item, index) in data" :key="index">
          <NTabPane :name="index" :tab="item.name">
            <div class="item p-l-60px p-t-66px b-rd-4px h-328px overflow-hidden">
              <div class="text-36px text-white lh-48px">
                {{ item.name }}({{ item.slug }})
              </div>
              <div class="text-16px text-white lh-22px m-t-10px opacity-80 w-50%">
                {{ item.summary }}
              </div>
              <NButton size="large" color="#fff" text-color="#0052d9" class="text-14px m-t-24px w-120px" @click="openLink(item.url)">
                免费使用
              </NButton>
            </div>
          </NTabPane>
        </template>
      </NTabs>
    </div>
  </div>
</template>

<style scoped lang="less">
.cloud {
  background-color: #ffffff;
  :deep(.n-tabs) {
    height: 328px;
    .n-tabs-nav {
      width: 180px;
      background: url('/assets/images/<EMAIL>') no-repeat center top;
      background-size: 100% 100%;
      padding: 16px 0;
    }
    .n-tabs-tab {
      padding: 12px 24px;
      &--active {
        font-weight: bold;
        background: linear-gradient(90deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
      }
    }
    .n-tabs-bar {
      left: 1px;
      right: auto;
    }
    .n-tab-pane {
      padding-left: 24px;
    }
    .item {
      background: url('/assets/images/<EMAIL>') no-repeat center;
      background-size: 100% 328px;
    }
  }
}
</style>
