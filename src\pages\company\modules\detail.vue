<script setup lang="ts">
defineOptions({
  name: 'CompanyDetail',
})

defineProps<{
  data: any
}>()

const imageBaseUrl = import.meta.env.VITE_STATIC_BASE_URL
</script>

<template>
  <div v-if="data" class="company-detail">
    <div class="p-24px b-rd-4px bg-white">
      <div class="text-28px lh-40px">
        {{ data.name }}
      </div>
      <div class="info text-16px lh-22px m-t-24px flex flex-col gap-y-16px">
        <div class="flex gap-x-8px items-center">
          <span class="text-16px font-bold">主营业务：</span>
          <span class="text-#4B5B76">高频焊复合铝管及高频焊冷拉拔复合铝管、集流管深加工、铝合金复合材料</span>
        </div>
        <div class="flex gap-x-8px items-center">
          <span class="text-16px font-bold">工厂工艺：</span>
          <span class="text-#4B5B76">高频焊,冷拉拔，冲压,熔炼</span>
        </div>
        <div class="flex gap-x-8px items-center">
          <span class="text-16px font-bold">公司地址：</span>
          <span class="text-#4B5B76">{{ data.address }}</span>
        </div>
      </div>
      <div class="detail m-t-24px prose" v-html="data.detail" />
      <div class="album m-t-24px">
        <div class="text-16px font-bold">
          企业相册：
        </div>
        <div class="m-t-12px flex gap-12px items-center">
          <template v-for="item in data.albums" :key="item.id">
            <NImage class="b-rd-8px h-160px w-250px" object-fit="cover" :src="imageBaseUrl + item.url" alt="cover" />
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less"></style>
