<script setup lang="ts">
import Job from './modules/job.vue'
import Resume from './modules/resume.vue'

defineOptions({
  name: 'TalentPage',
})

const route = useRoute()
const router = useRouter()

const active = ref(route.query.tab ? Number(route.query.tab) : 0)

watch(active, (val) => {
  router.replace({ query: { ...route.query, tab: val } })
})
</script>

<template>
  <main>
    <NBreadcrumb separator=">" class="text-16px p-t-16px container">
      <NBreadcrumbItem href="/" class="flex gap-x-4px items-end">
        <div class="flex gap-x-4px items-end">
          <i class="i-carbon-home" />
          <span>首页</span>
        </div>
      </NBreadcrumbItem>
      <NBreadcrumbItem>
        企业人才管理
      </NBreadcrumbItem>
    </NBreadcrumb>
    <div class="p-y-16px container">
      <NTabs v-model:value="active" type="line">
        <NTabPane :name="0" tab="人才需求">
          <Job />
        </NTabPane>
        <NTabPane :name="1" tab="企业人才库">
          <Resume />
        </NTabPane>
      </NTabs>
    </div>
  </main>
</template>

<style scoped lang="less">
.container {
  :deep(.n-tabs) {
    --n-tab-gap: 48px !important;
    .n-tabs-nav {
      background-color: #fff;
      .n-tabs-wrapper {
        padding: 8px 24px;
        .n-tabs-tab__label {
          color: #4b5b76;
          font-size: 20px;
          line-height: 28px;
        }
        .n-tabs-tab--active {
          .n-tabs-tab__label {
            color: #181818;
            font-weight: bold;
          }
        }
      }
    }
    .n-tabs-pane-wrapper {
      overflow: visible;
    }
    .n-tab-pane {
      --n-pane-text-color: #181818;
      padding: 0;
    }
  }
}
</style>
