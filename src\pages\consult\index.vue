<script setup lang="ts">
import Banner from './modules/banner.vue'
import Report from './modules/report.vue'
import Service from './modules/service.vue'

defineOptions({
  name: 'ConsultPage',
})

const route = useRoute()
const router = useRouter()

const active = ref(route.query.tab ? Number(route.query.tab) : 0)

watch(active, (val) => {
  router.replace({ query: { ...route.query, tab: val } })
})
</script>

<template>
  <main>
    <Banner />
    <div class="p-y-32px container">
      <NTabs v-model:value="active" type="line" class="bg-white">
        <NTabPane :name="0" tab="行业数字化诊断报告">
          <Report />
        </NTabPane>
        <NTabPane :name="1" tab="企业管理咨询">
          <Service />
        </NTabPane>
      </NTabs>
    </div>
  </main>
</template>

<style scoped lang="less">
.container {
  :deep(.n-tabs) {
    --n-tab-gap: 48px !important;
    .n-tabs-nav {
      .n-tabs-wrapper {
        padding: 8px 24px;
        .n-tabs-tab__label {
          color: #4b5b76;
          font-size: 20px;
          line-height: 28px;
        }
        .n-tabs-tab--active {
          .n-tabs-tab__label {
            color: #181818;
            font-weight: bold;
          }
        }
      }
    }
    .n-tab-pane {
      --n-pane-text-color: #181818;
    }
  }
}
</style>
