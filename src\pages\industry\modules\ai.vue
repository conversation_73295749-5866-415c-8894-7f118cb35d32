<script setup lang="ts">
import { RouterLink } from 'vue-router'

defineOptions({
  name: 'IndustryAi',
})
</script>

<template>
  <div class="ai b-rd-4px bg-white overflow-hidden">
    <div class="hd text-24px lh-72px font-bold p-l-90px">
      AI智能问答
    </div>
    <div class="bd p-24px">
      <p class="text-16px text-#4B5B76 lh-28px">
        Hi~ 我是溱东数字管家，有问题可<RouterLink to="/industry/ai" class="text-primary">
          直接咨询
        </RouterLink>我哦，快来体验吧~
      </p>
    </div>
  </div>
</template>

<style scoped lang="less">
.ai {
  width: 100%;
  .hd {
    width: 100%;
    height: 72px;
    background: url('/assets/images/<EMAIL>') no-repeat center center / 100% 100%;
  }
  .bd {
    width: 100%;
    height: 100%;
  }
}
</style>
