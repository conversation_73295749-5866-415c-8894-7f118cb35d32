<script setup lang="ts">
import { baseApi } from '~/service/modules/base'
import { useDictStore } from '~/stores'

defineOptions({
  name: 'IndustryDoc',
})

const useDict = useDictStore()

const imageBaseUrl = import.meta.env.VITE_STATIC_BASE_URL

const list = ref<any>()

const industry = ref(0)

const filter = ref({
  _page: 1,
  _limit: 20,
  q: '',
  industry,
})

async function getData() {
  const res = await baseApi.docList(filter.value)
  if (res.code === 200) {
    list.value = res.data
  }
}

onMounted(() => {
  getData()
})

watch(industry, () => getData())
</script>

<template>
  <div class="doc">
    <div class="search p-24px bg-white">
      <div class="keyword p-x-32px b-rd-4px flex h-128px w-full items-center">
        <div class="text-white w-40%">
          <div class="text-28px lh-40px">
            行业知识库
          </div>
          <div class="text-16px lh-22px m-t-8px opacity-80">
            汇聚前沿资讯，洞见未来趋势
          </div>
        </div>
        <NInputGroup class="w-60%">
          <NInput v-model:value="filter.q" size="large" placeholder="请输入关键词" clearable>
            <template #prefix>
              <span class="i-carbon-search text-16px" />
            </template>
          </NInput>
          <NButton size="large" type="info" class="w-104px" @click="getData">
            查询
          </NButton>
        </NInputGroup>
      </div>
      <div class="text-18px lh-25px m-t-24px filter">
        <div class="flex items-center justify-between">
          <div class="label text-#4B5B76 text-right w-120px">
            所属行业：
          </div>
          <div class="w-650px">
            <NRadioGroup v-model:value="industry">
              <NRadioButton :key="0" :value="0">
                全部
              </NRadioButton>
              <NRadioButton v-for="item in useDict.get('Industry')" :key="item.value" :value="item.value">
                {{ item.label }}
              </NRadioButton>
            </NRadioGroup>
          </div>
        </div>
      </div>
    </div>
    <div class="list m-t-24px">
      <div v-if="list?.records.length > 0" class="bg-white flex flex-col">
        <RouterLink v-for="item in list?.records" :key="item.id" :to="`/industry/doc_${item.id}`" class="item m-x-24px p-y-24px flex justify-between hover:text-primary">
          <div class="cover b-rd-4px h-128px w-180px overflow-hidden">
            <img class="h-full w-full" :src="imageBaseUrl + item.cover" alt="cover">
          </div>
          <div class="info w-548px">
            <div class="title text-20px lh-28px font-medium line-clamp-1">
              {{ item.title }}
            </div>
            <div class="summary text-16px text-#4B5B76 lh-24px m-t-8px line-clamp-2">
              {{ item.summary }}
            </div>
            <div class="m-t-16px flex gap-x-8px">
              <NTag v-for="tag in item.tags" :key="item.id + tag" :color="{ color: '#F1F2F5', textColor: '#4B5B76' }" size="small" :bordered="false">
                {{ tag }}
              </NTag>
            </div>
          </div>
        </RouterLink>
      </div>
      <NEmpty v-else size="large" description="暂无数据" class="p-y-120px" />
    </div>
    <div class="m-t-36px flex items-center justify-center">
      <NPagination
        v-model:page="filter._page"
        :page-count="list?.total"
        :page-size="filter._limit"
        size="large"
        show-quick-jumper
      />
    </div>
  </div>
</template>

<style scoped lang="less">
.doc {
  padding-bottom: 40px;
  .search {
    .keyword {
      background: url('/assets/images/<EMAIL>') no-repeat center center / cover;
      :deep(.n-input) {
        --n-height: 56px !important;
        --n-color: #f5f7fa !important;
        --n-border: #f5f7fa !important;
        --n-font-size: 16px !important;
      }
      :deep(.n-button) {
        --n-height: 56px !important;
      }
    }
    .filter {
      .line {
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f5f7fa;
      }
      :deep(.n-radio-group) {
        display: flex;
        gap: 0 24px;
        .n-radio-group__splitor,
        .n-radio-button__state-border {
          display: none;
        }
        .n-radio-button {
          border: 0;
          border-radius: 4px;
          font-size: 18px;
          color: #181818;
        }
        .n-radio-button--checked {
          background: #ccdeff;
          color: #0052d9;
        }
      }
    }
  }
  .list {
    .item {
      .cover img {
        transition: all 0.3s ease-in-out;
      }
      &:hover {
        .cover img {
          transform: scale(1.1);
        }
      }
    }
  }
}
</style>
