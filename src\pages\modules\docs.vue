<script setup lang="ts">
defineOptions({
  name: 'HomeDocs',
})

const list = [
  {
    title: '知识标题知识标题',
    cover: '/upload/img1.png',
    summary: '文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本',
    url: '/',
  },
  {
    title: '知识标题知识标题',
    cover: '/upload/img2.png',
    summary: '文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本',
    url: '/',
  },
  {
    title: '知识标题知识标题',
    cover: '/upload/img3.png',
    summary: '文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本',
    url: '/',
  },
  {
    title: '知识标题知识标题',
    cover: '/upload/img3.png',
    summary: '文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本文本',
    url: '/',
  },
]
</script>

<template>
  <div class="docs">
    <div class="p-y-64px container">
      <div class="text-28px lh-48px text-center">
        行业知识
      </div>
      <div class="text-16px text-#4B5B76 lh-48px m-t-8px flex gap-x-8px items-center justify-center">
        <span>助力企业以变革谋未来</span>
        <RouterLink to="/industry" class="text-primary flex items-center justify-center hover:opacity-50">
          <span>查看更多</span>
          <span class="i-carbon-chevron-right text-[0.9em] text-primary" />
        </RouterLink>
      </div>
      <div v-if="list.length > 0" class="m-t-32px flex items-center justify-between">
        <RouterLink :to="list[0].url" class="hot text-white b-rd-4px h-354px w-588px overflow-hidden hover:text-primary">
          <div class="cover h-full w-full">
            <img class="h-full w-full" :src="list[0].cover" alt="cover">
          </div>
          <div class="info p-x-24px py-16px">
            <div class="title text-20px lh-28px line-clamp-1">
              {{ list[0].title }}
            </div>
            <div class="summary text-16px text-white lh-24px m-t-8px opacity-80 line-clamp-2">
              {{ list[0].summary }}
            </div>
          </div>
        </RouterLink>
        <div class="list flex flex-col gap-y-24px w-590px">
          <template v-for="(item, index) in list" :key="item.url">
            <RouterLink v-if="index !== 0" :to="item.url" class="item flex justify-between hover:text-primary">
              <div class="cover b-rd-4px h-102px w-180px overflow-hidden">
                <img class="h-full w-full" :src="item.cover" alt="cover">
              </div>
              <div class="info w-392px">
                <div class="title text-20px lh-28px line-clamp-1">
                  {{ item.title }}
                </div>
                <div class="summary text-16px text-#4B5B76 lh-24px m-t-8px opacity-80 line-clamp-2">
                  {{ item.summary }}
                </div>
              </div>
            </RouterLink>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.docs {
  .hot {
    position: relative;
    .info {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      background: rgba(0, 0, 0, 0.6);
    }
  }
  .hot {
    .cover img {
      transition: all 0.2s ease-in-out;
    }
    &:hover {
      .cover img {
        transform: scale(1.1);
      }
    }
  }
  .list {
    .item {
      .cover img {
        transition: all 0.2s ease-in-out;
      }
      &:hover {
        .cover img {
          transform: scale(1.1);
        }
      }
    }
  }
}
</style>
