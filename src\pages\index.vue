<script setup lang="ts">
import { baseApi } from '~/service/modules/base'
import Banner from './modules/banner.vue'
import Cloud from './modules/cloud.vue'
import Company from './modules/company.vue'
import Consult from './modules/consult.vue'
import Docs from './modules/docs.vue'
import Expert from './modules/expert.vue'
import Report from './modules/report.vue'
import Talent from './modules/talent.vue'

defineOptions({
  name: 'IndexPage',
})

const homeData = ref<any>({})

async function getData() {
  const res = await baseApi.home()
  if (res.code === 200) {
    homeData.value = res.data
  }
}

onMounted(() => {
  getData()
})
</script>

<template>
  <main>
    <Banner :data="homeData.banners" />
    <Cloud :data="homeData.apps" />
    <Consult />
    <Report :data="homeData.reports" />
    <Expert :data="homeData.experts" />
    <Docs :data="homeData.knowledges" />
    <Talent :data="homeData.jobs" />
    <Company :data="homeData.companies" />
  </main>
</template>

<style scoped lang="less">

</style>
