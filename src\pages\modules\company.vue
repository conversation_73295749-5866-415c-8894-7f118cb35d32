<script setup lang="ts">
defineOptions({
  name: 'HomeCompany',
})

defineProps<{
  data: any[] | undefined
}>()

const imageBaseUrl = import.meta.env.VITE_STATIC_BASE_URL
</script>

<template>
  <div class="company">
    <div class="p-y-64px overflow-hidden container">
      <div class="text-28px lh-48px text-center">
        企业库
      </div>
      <div class="text-16px text-#4B5B76 lh-48px m-t-8px flex gap-x-8px items-center justify-center">
        <span>以精立业，以质取胜</span>
        <RouterLink to="/company" class="text-primary m-l-8px flex items-center justify-center hover:opacity-50">
          <span>查看更多</span>
          <span class="i-carbon-chevron-right text-[0.9em] text-primary" />
        </RouterLink>
      </div>

      <NCarousel :slides-per-view="4" :show-dots="false" :space-between="23" :loop="false" draggable class="m-t-32px">
        <template v-for="item in data" :key="item.id">
          <NCarouselItem>
            <RouterLink :to="item.url" class="hover:text-primary">
              <div class="item cursor-pointer">
                <div class="cover b-rd-4px overflow-hidden">
                  <img :src="imageBaseUrl + (item.albums.length > 0 ? item.albums[0].url : item.logo)" alt="cover">
                </div>
                <div class="info">
                  <div class="name text-16px lh-22px font-bold">
                    {{ item.name }}
                  </div>
                  <div class="slogan text-14px text-#4B5B76 lh-24px m-t-8px">
                    {{ item.summary }}
                  </div>
                </div>
              </div>
            </RouterLink>
          </NCarouselItem>
        </template>
      </NCarousel>
    </div>
  </div>
</template>

<style scoped lang="less">
.company {
  .n-carousel__slide {
    padding-top: 32px;
    .item {
      position: relative;
      height: 254px;
      border-radius: 4px;
      overflow: hidden;
      transition: all 0.2s ease-in-out;
      &:hover {
        transform: translateY(-16px);
        .info {
          background-size: 100% 100%;
        }
      }
      .cover img {
        height: 182px;
        width: 100%;
      }
      .info {
        position: absolute;
        left: 16px;
        bottom: 24px;
        background: rgba(0, 0, 0, 0.6);
        width: calc(100% - 32px);
        padding: 16px;
        box-shadow: 0px 8px 16px 0px rgba(192, 199, 218, 0.3);
        border-radius: 4px;
        background: url('/assets/images/<EMAIL>') no-repeat center center / 800% 800%;
        transition: all 0.2s ease-in-out;
      }
    }
  }
}
</style>
