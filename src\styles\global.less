html,
body,
#app {
  height: 100%;
  margin: 0;
  padding: 0;
  background: #f5f7fa;
  a {
    transition: all 0.2s ease-in-out;
  }
  .container {
    width: 1200px;
    margin: 0 auto;
  }
  main {
    min-height: 75vh;
  }
}

html.dark {
  color-scheme: dark;
}

.prose {
  max-width: 100%;
}

.n-breadcrumb {
  ul {
    display: flex;
    align-items: center;
    .n-breadcrumb-item__separator {
      margin: 0 4px !important;
    }
  }
}
