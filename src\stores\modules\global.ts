import { acceptHMRUpdate, defineStore } from 'pinia'
import { ref } from 'vue'

export const useGlobalStore = defineStore(
  'global',
  () => {
    // 全局Loading
    const loading = ref()

    function showLoading() {
      loading.value = true
    }

    function hideLoading() {
      loading.value = false
    }

    return {
      loading,
      showLoading,
      hideLoading,
    }
  },
)

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useGlobalStore as any, import.meta.hot))
