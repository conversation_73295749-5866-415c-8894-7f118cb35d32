{"type": "module", "private": true, "packageManager": "pnpm@10.11.1", "scripts": {"build": "vite build", "dev": "vite --port 3333 --open", "lint": "eslint .", "typecheck": "vue-tsc", "preview": "vite preview", "test": "vitest", "up": "taze major -I", "postinstall": "npx simple-git-hooks"}, "dependencies": {"@vant/area-data": "catalog:frontend", "@vueuse/core": "catalog:frontend", "axios": "catalog:frontend", "dayjs": "catalog:frontend", "jr-qrcode": "catalog:frontend", "motion-v": "catalog:frontend", "pinia": "catalog:frontend", "pinia-plugin-persistedstate": "catalog:frontend", "vue": "catalog:frontend", "vue-router": "catalog:frontend"}, "devDependencies": {"@antfu/eslint-config": "catalog:dev", "@iconify-json/carbon": "catalog:build", "@types/node": "catalog:dev", "@unocss/eslint-plugin": "catalog:dev", "@unocss/reset": "catalog:dev", "@vitejs/plugin-vue": "catalog:build", "@vue-macros/volar": "catalog:dev", "@vue/test-utils": "catalog:dev", "eslint": "catalog:dev", "eslint-plugin-format": "catalog:dev", "jsdom": "catalog:build", "less": "catalog:dev", "lint-staged": "catalog:dev", "naive-ui": "catalog:dev", "simple-git-hooks": "catalog:dev", "taze": "catalog:dev", "terser": "catalog:dev", "typescript": "catalog:dev", "unocss": "catalog:build", "unplugin-auto-import": "catalog:build", "unplugin-vue-components": "catalog:build", "unplugin-vue-macros": "catalog:build", "unplugin-vue-router": "catalog:build", "vite": "catalog:build", "vite-plugin-svg-icons": "catalog:build", "vitest": "catalog:dev", "vue-tsc": "catalog:dev"}, "resolutions": {"unplugin": "catalog:build", "vite": "catalog:build"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}