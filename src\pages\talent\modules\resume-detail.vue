<script setup lang="ts">
defineOptions({
  name: 'TalentJobDetail',
})

defineProps<{
  data: any
}>()

const imageBaseUrl = import.meta.env.VITE_STATIC_BASE_URL

function scrollBottom() {
  const el = document.querySelector('.resume-detail')
  if (el) {
    el.scrollIntoView({ behavior: 'smooth' })
  }
}
</script>

<template>
  <div v-if="data" class="resume-detail">
    <div class="info flex justify-between">
      <div class="avatar b-rd-4px overflow-hidden">
        <img class="h-152px w-152px" :src="imageBaseUrl + data.avatar" alt="avatar">
      </div>
      <div class="w-950px">
        <div class="text-28px lh-40px font-medium flex gap-x-16px items-start justify-between">
          <span>{{ data.name }}</span>
          <NButton type="primary" ghost size="small" class="text-18px h-56px w-152px" @click="scrollBottom">
            <div class="flex gap-x-8px items-center">
              <i class="i-carbon-document-download" />
              <span>下载简历</span>
            </div>
          </NButton>
        </div>
        <div class="text-18px lh-25px">
          <span>专业技能：</span>
          <span>{{ data.skills.join('，') }}</span>
        </div>
        <div class="text-18px lh-25px m-t-8px">
          <span>电话：</span>
          <span>{{ data.phone }}</span>
        </div>
        <div class="text-18px lh-25px m-t-8px">
          <span>邮箱：</span>
          <span>{{ data.email }}</span>
        </div>
      </div>
    </div>
    <div class="detail">
      <div class="profile">
        <div class="hd flex gap-x-8px items-center">
          <img src="/assets/images/<EMAIL>" alt="avatar" class="h-18px w-18px">
          <span>基本信息</span>
        </div>
        <div class="bd p-24px flex flex-wrap gap-16px items-center">
          <div class="item">
            <span>姓名：</span>
            <span class="text-#4B5B76">{{ data.name }}</span>
          </div>
          <div class="item">
            <span>性别：</span>
            <span class="text-#4B5B76">{{ data.gender }}</span>
          </div>
          <div class="item">
            <span>年龄：</span>
            <span class="text-#4B5B76">{{ data.age }}岁</span>
          </div>
          <div class="item">
            <span>学历：</span>
            <span class="text-#4B5B76">{{ data.edu }}</span>
          </div>
          <div class="item">
            <span>工作年限：</span>
            <span class="text-#4B5B76">{{ data.exp }}年</span>
          </div>
        </div>
      </div>
      <div class="concact m-t-24px">
        <div class="hd flex gap-x-8px items-center">
          <img src="/assets/images/<EMAIL>" alt="avatar" class="h-18px w-18px">
          <span>紧急联系人</span>
        </div>
        <div class="bd p-24px flex flex-wrap gap-16px items-center">
          <div class="item">
            <span>联系人1：</span>
            <span class="text-#4B5B76">李国华  |  56岁  |  父女  |  13456787765</span>
          </div>
        </div>
      </div>
      <div class="detail m-t-24px">
        <div class="hd flex gap-x-8px items-center">
          <img src="/assets/images/<EMAIL>" alt="avatar" class="h-18px w-18px">
          <span>详细信息</span>
        </div>
        <div class="bd p-24px prose" v-html="data.detail" />
        <div v-if="data.files && data.files.length > 0" class="attach m-t-16px p-16px bg-gray-100">
          <h4>简历附件：</h4>
          <ul class="m-t-8px">
            <li v-for="(item, index) in data.files" :key="index">
              <a class="text-primary hover:opacity-80" :href="imageBaseUrl + item.url" target="_blank">《{{ item.name }}》</a>
            </li>
          </ul>
        </div>
      </div>
      <!-- <div class="edu m-t-24px">
        <div class="hd flex gap-x-8px items-center">
          <img src="/assets/images/<EMAIL>" alt="avatar" class="h-18px w-18px">
          <span>教育背景</span>
        </div>
        <div class="bd p-24px">
          <div class="item flex gap-x-16px items-center">
            <span>2013/09-2017/09</span>
            <span>东京大学网络教育学院 | 本科 | 工业设计</span>
          </div>
          <div class="item flex gap-x-16px items-center">
            <span>2013/09-2017/09</span>
            <span>东京大学网络教育学院 | 本科 | 工业设计</span>
          </div>
        </div>
      </div>
      <div class="exp m-t-24px">
        <div class="hd flex gap-x-8px items-center">
          <img src="/assets/images/<EMAIL>" alt="avatar" class="h-18px w-18px">
          <span>工作经历</span>
        </div>
        <div class="bd p-24px">
          <div class="item flex gap-x-16px items-center">
            <span>2013/09-2017/09</span>
            <span>东京麒转株式会社 | 设计师</span>
          </div>
          <div class="item flex gap-x-16px items-center">
            <span>2013/09-2017/09</span>
            <span>东京麒转株式会社 | 设计师</span>
          </div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<style scoped lang="less">
.resume-detail {
  background: #ffffff;
  padding: 32px;
  .detail {
    margin-top: 32px;
    padding: 32px 0;
    border-top: 1px solid rgba(75, 91, 118, 0.15);
    .hd {
      font-size: 20px;
      line-height: 1;
      font-weight: bold;
    }
  }
  .profile {
    .item {
      width: 32%;
      font-size: 18px;
      line-height: 25px;
    }
  }
  .concact {
    .item {
      width: 100%;
      font-size: 18px;
      line-height: 25px;
    }
  }
  .edu,
  .exp {
    .item {
      font-size: 18px;
      line-height: 50px;
      color: #4b5b76;
    }
  }
}
</style>
