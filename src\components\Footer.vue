<script setup lang="ts">
import { useConfigStore } from '~/stores'

const links = ref({
  hot: {
    title: '热门推荐',
    list: [
      {
        name: '企业数字化诊断报告',
        path: '/consult',
      },
      {
        name: '企业咨询管理',
        path: '/consult?tab=1',
      },
      {
        name: '行业知识',
        path: '/industry',
      },
      {
        name: '行业专家',
        path: '/industry?tab=1',
      },
      {
        name: '企业人才管理',
        path: '/talent',
      },
    ],
  },
  knowledge: {
    title: '行业知识库',
    list: [
      {
        name: '行业知识',
        path: '/industry',
      },
      {
        name: '行业专家',
        path: '/industry?tab=1',
      },
    ],
  },
  talent: {
    title: '企业人才管理',
    list: [
      {
        name: '人才需求',
        path: '/talent',
      },
      {
        name: '企业人才库',
        path: '/talent?tab=1',
      },
    ],
  },
  company: {
    title: '企业库',
    list: [
      {
        name: '企业库',
        path: '/company',
      },
    ],
  },
  consult: {
    title: '友情链接',
    list: [] as any,
  },
})

const useConfig = useConfigStore()

// 友情链接
links.value.consult.list = useConfig.get('FriendLink').map((item: any) => {
  return {
    name: item.name,
    path: item.value,
  }
})
</script>

<template>
  <div class="footer flex flex-col items-center justify-center">
    <div class="p-y-50px flex h-full w-1360px justify-between">
      <div v-for="link in links" :key="link.title" class="text-14px text-white">
        <div class="text-16px text-white lh-22px">
          {{ link.title }}
        </div>
        <div class="m-t-16px flex flex-col gap-y-12px">
          <RouterLink v-for="item in link.list" :key="item.path" :to="item.path.startsWith('/') ? item.path : ''" :href="item.path.startsWith('/') ? '' : item.path" :target="item.path.startsWith('/') ? '_self' : '_blank'" class="opacity-50 hover:text-primary hover:opacity-100">
            {{ item.name }}
          </RouterLink>
        </div>
      </div>
      <div class="contact text-14px text-white">
        <div class="text-16px text-white lh-22px">
          联系我们
        </div>
        <div class="m-t-16px flex flex-col gap-y-12px">
          <span class="opacity-50">联系电话：{{ useConfig.getValue('Contact', 'Phone') }}</span>
          <span class="opacity-50">联系邮箱：{{ useConfig.getValue('Contact', 'Email') }}</span>
          <span class="opacity-50">联系地址：{{ useConfig.getValue('Contact', 'Address') }}</span>
          <div class="m-t-16px">
            <QRCode class="h-100px w-100px" :value="useConfig.getValue('Contact', 'Qrcode')" />
          </div>
        </div>
      </div>
    </div>
    <div class="copyright text-14px text-white p-y-16px text-center flex gap-x-16px w-full justify-center">
      <RouterLink to="/page/privacy" class="opacity-50 hover:text-primary hover:opacity-100">
        隐私协议
      </RouterLink>
      <RouterLink to="/page/service" class="opacity-50 hover:text-primary hover:opacity-100">
        服务协议
      </RouterLink>
      <span class="opacity-50">{{ useConfig.getValue('System', 'Copyright') }}</span>
    </div>
  </div>
</template>

<style scoped lang="less">
.footer {
  width: 100%;
  background-color: #242933;
  .copyright {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
  }
}
</style>
