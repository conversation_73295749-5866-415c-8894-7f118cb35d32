import { acceptHMRUpdate, defineStore } from 'pinia'
import { ref } from 'vue'
import { baseApi } from '~/service/modules/base'

export const useDictStore = defineStore(
  'dict',
  () => {
    // 全局字典数据
    const dict = ref<Api.Base.Dict[]>()

    // 获取字典数据
    async function init() {
      if (dict.value) {
        return
      }
      try {
        const res = await baseApi.dict()
        if (res.code === 200) {
          dict.value = res.data
        }
      }
      catch (error) {
        console.error('获取配置数据失败:', error)
      }
    }

    // 自动初始化
    init()

    // 根据字典编码获取字典项
    function get(code: string) {
      return dict.value?.find(item => item.code === code)?.options || []
    }

    // 根据字典编码和值获取字典项标签
    function getLabel(code: string, value: any) {
      return get(code).find(item => item.value === String(value))?.label || value
    }

    return {
      dict,
      init,
      get,
      getLabel,
    }
  },
  {
    persist: {
      storage: localStorage,
      pick: ['dict'],
    },
  },
)

if (import.meta.hot)
  import.meta.hot.accept(acceptHMRUpdate(useDictStore as any, import.meta.hot))
