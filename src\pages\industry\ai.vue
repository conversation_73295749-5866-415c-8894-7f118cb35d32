<script setup lang="ts">
defineOptions({
  name: 'IndustryAiPage',
})
</script>

<template>
  <main class="flex flex-col">
    <NBreadcrumb separator=">" class="text-16px p-t-16px container">
      <NBreadcrumbItem href="/" class="flex gap-x-4px items-end">
        <div class="flex gap-x-4px items-end">
          <i class="i-carbon-home" />
          <span>首页</span>
        </div>
      </NBreadcrumbItem>
      <NBreadcrumbItem @click="$router.back()">
        行业知识库
      </NBreadcrumbItem>
      <NBreadcrumbItem>
        溱东智能助手
      </NBreadcrumbItem>
    </NBreadcrumb>
    <div class="p-t-16px container">
      <iframe
        src="https://qdrag.kscss.com/chat/share?shared_id=74861ac862b411f0a6620242ac170006&from=agent&auth=k3ODk5MWZlNjJiMzExZjA5NGUyMDI0Mm"
        class="h-full min-h-67vh w-full"
        frameborder="0"
      />
    </div>
  </main>
</template>

<style scoped lang="less">
body {
  height: 100vh;
  overflow: hidden;
}
</style>
